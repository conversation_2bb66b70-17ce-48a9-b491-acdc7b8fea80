import cv2
import numpy as np
import argparse
import os

def nv12_to_bgr(input_path, output_path, width, height):
    """
    将 NV12 二进制文件转换为 BGR 格式的图片
    :param input_path: 输入的 NV12 文件路径
    :param output_path: 输出的 BGR 图片路径
    :param width: 图像宽度 (3840)
    :param height: 图像高度 (2160)
    """
    try:
        # 计算 NV12 文件预期大小
        expected_size = width * height * 3 // 2
        
        # 读取二进制数据
        with open(input_path, 'rb') as f:
            nv12_data = f.read()
        
        # 验证文件大小
        if len(nv12_data) != expected_size:
            raise ValueError(f"文件大小错误: 预期 {expected_size} 字节, 实际 {len(nv12_data)} 字节")
        
        # 转换为 numpy 数组并重塑形状
        nv12_array = np.frombuffer(nv12_data, dtype=np.uint8)
        bgr_image = nv12_array.reshape((height * 3 // 2, width,1))  # 关键步骤：NV12 格式的特殊形状
        bgr_image = cv2.cvtColor(bgr_image, cv2.COLOR_YUV2BGR_NV12)  # 关键步骤：颜色空间转换
        
        # 执行颜色空间转换
 
        # 保存结果
        cv2.imwrite(output_path, bgr_image)
        print(f"转换成功! 结果已保存至: {output_path}")
        
        return True
    
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 创建命令行参数解析器

    width = 3840 
    height = 2160 
    srcdir = r"D:/Dataset/image/calibrate/nv12/imgs"
    dstdir = r"D:/Dataset/image/calibrate/nv12/jpg"

    for fn in os.listdir(srcdir):
        nv12_to_bgr(os.path.join(srcdir,fn), os.path.join(dstdir,fn.replace(".bin",".jpg")), width, height)