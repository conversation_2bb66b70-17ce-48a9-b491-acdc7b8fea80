# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = "/D/Program Files/CMake/bin/cmake.exe"

# The command to remove a file.
RM = "/D/Program Files/CMake/bin/cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /D/Program/Project/project/czcv_camera_new

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"/D/Program Files/CMake/bin/cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"/D/Program Files/CMake/bin/cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/lib//CMakeFiles/progress.marks
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/all
	$(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
lib/CMakeFiles/czcv_camera.dir/rule:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/czcv_camera.dir/rule
.PHONY : lib/CMakeFiles/czcv_camera.dir/rule

# Convenience name for target.
czcv_camera: lib/CMakeFiles/czcv_camera.dir/rule
.PHONY : czcv_camera

# fast build rule for target.
czcv_camera/fast:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/build
.PHONY : czcv_camera/fast

src/base/abstract_model.o: src/base/abstract_model.cpp.o
.PHONY : src/base/abstract_model.o

# target to build an object file
src/base/abstract_model.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.o
.PHONY : src/base/abstract_model.cpp.o

src/base/abstract_model.i: src/base/abstract_model.cpp.i
.PHONY : src/base/abstract_model.i

# target to preprocess a source file
src/base/abstract_model.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.i
.PHONY : src/base/abstract_model.cpp.i

src/base/abstract_model.s: src/base/abstract_model.cpp.s
.PHONY : src/base/abstract_model.s

# target to generate assembly for a file
src/base/abstract_model.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/abstract_model.cpp.s
.PHONY : src/base/abstract_model.cpp.s

src/base/aes256.o: src/base/aes256.cpp.o
.PHONY : src/base/aes256.o

# target to build an object file
src/base/aes256.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.o
.PHONY : src/base/aes256.cpp.o

src/base/aes256.i: src/base/aes256.cpp.i
.PHONY : src/base/aes256.i

# target to preprocess a source file
src/base/aes256.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.i
.PHONY : src/base/aes256.cpp.i

src/base/aes256.s: src/base/aes256.cpp.s
.PHONY : src/base/aes256.s

# target to generate assembly for a file
src/base/aes256.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/aes256.cpp.s
.PHONY : src/base/aes256.cpp.s

src/base/common.o: src/base/common.cpp.o
.PHONY : src/base/common.o

# target to build an object file
src/base/common.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.o
.PHONY : src/base/common.cpp.o

src/base/common.i: src/base/common.cpp.i
.PHONY : src/base/common.i

# target to preprocess a source file
src/base/common.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.i
.PHONY : src/base/common.cpp.i

src/base/common.s: src/base/common.cpp.s
.PHONY : src/base/common.s

# target to generate assembly for a file
src/base/common.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/common.cpp.s
.PHONY : src/base/common.cpp.s

src/base/macro.o: src/base/macro.cpp.o
.PHONY : src/base/macro.o

# target to build an object file
src/base/macro.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.o
.PHONY : src/base/macro.cpp.o

src/base/macro.i: src/base/macro.cpp.i
.PHONY : src/base/macro.i

# target to preprocess a source file
src/base/macro.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.i
.PHONY : src/base/macro.cpp.i

src/base/macro.s: src/base/macro.cpp.s
.PHONY : src/base/macro.s

# target to generate assembly for a file
src/base/macro.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/macro.cpp.s
.PHONY : src/base/macro.cpp.s

src/base/mem_allocator.o: src/base/mem_allocator.cpp.o
.PHONY : src/base/mem_allocator.o

# target to build an object file
src/base/mem_allocator.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.o
.PHONY : src/base/mem_allocator.cpp.o

src/base/mem_allocator.i: src/base/mem_allocator.cpp.i
.PHONY : src/base/mem_allocator.i

# target to preprocess a source file
src/base/mem_allocator.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.i
.PHONY : src/base/mem_allocator.cpp.i

src/base/mem_allocator.s: src/base/mem_allocator.cpp.s
.PHONY : src/base/mem_allocator.s

# target to generate assembly for a file
src/base/mem_allocator.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/mem_allocator.cpp.s
.PHONY : src/base/mem_allocator.cpp.s

src/base/nms.o: src/base/nms.cpp.o
.PHONY : src/base/nms.o

# target to build an object file
src/base/nms.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.o
.PHONY : src/base/nms.cpp.o

src/base/nms.i: src/base/nms.cpp.i
.PHONY : src/base/nms.i

# target to preprocess a source file
src/base/nms.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.i
.PHONY : src/base/nms.cpp.i

src/base/nms.s: src/base/nms.cpp.s
.PHONY : src/base/nms.s

# target to generate assembly for a file
src/base/nms.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/nms.cpp.s
.PHONY : src/base/nms.cpp.s

src/base/status.o: src/base/status.cpp.o
.PHONY : src/base/status.o

# target to build an object file
src/base/status.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.o
.PHONY : src/base/status.cpp.o

src/base/status.i: src/base/status.cpp.i
.PHONY : src/base/status.i

# target to preprocess a source file
src/base/status.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.i
.PHONY : src/base/status.cpp.i

src/base/status.s: src/base/status.cpp.s
.PHONY : src/base/status.s

# target to generate assembly for a file
src/base/status.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/base/status.cpp.s
.PHONY : src/base/status.cpp.s

src/center_stage/cam_dewarper.o: src/center_stage/cam_dewarper.cpp.o
.PHONY : src/center_stage/cam_dewarper.o

# target to build an object file
src/center_stage/cam_dewarper.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.o
.PHONY : src/center_stage/cam_dewarper.cpp.o

src/center_stage/cam_dewarper.i: src/center_stage/cam_dewarper.cpp.i
.PHONY : src/center_stage/cam_dewarper.i

# target to preprocess a source file
src/center_stage/cam_dewarper.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.i
.PHONY : src/center_stage/cam_dewarper.cpp.i

src/center_stage/cam_dewarper.s: src/center_stage/cam_dewarper.cpp.s
.PHONY : src/center_stage/cam_dewarper.s

# target to generate assembly for a file
src/center_stage/cam_dewarper.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/cam_dewarper.cpp.s
.PHONY : src/center_stage/cam_dewarper.cpp.s

src/center_stage/center_stage_api.o: src/center_stage/center_stage_api.cpp.o
.PHONY : src/center_stage/center_stage_api.o

# target to build an object file
src/center_stage/center_stage_api.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.o
.PHONY : src/center_stage/center_stage_api.cpp.o

src/center_stage/center_stage_api.i: src/center_stage/center_stage_api.cpp.i
.PHONY : src/center_stage/center_stage_api.i

# target to preprocess a source file
src/center_stage/center_stage_api.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.i
.PHONY : src/center_stage/center_stage_api.cpp.i

src/center_stage/center_stage_api.s: src/center_stage/center_stage_api.cpp.s
.PHONY : src/center_stage/center_stage_api.s

# target to generate assembly for a file
src/center_stage/center_stage_api.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_api.cpp.s
.PHONY : src/center_stage/center_stage_api.cpp.s

src/center_stage/center_stage_capi.o: src/center_stage/center_stage_capi.cpp.o
.PHONY : src/center_stage/center_stage_capi.o

# target to build an object file
src/center_stage/center_stage_capi.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.o
.PHONY : src/center_stage/center_stage_capi.cpp.o

src/center_stage/center_stage_capi.i: src/center_stage/center_stage_capi.cpp.i
.PHONY : src/center_stage/center_stage_capi.i

# target to preprocess a source file
src/center_stage/center_stage_capi.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.i
.PHONY : src/center_stage/center_stage_capi.cpp.i

src/center_stage/center_stage_capi.s: src/center_stage/center_stage_capi.cpp.s
.PHONY : src/center_stage/center_stage_capi.s

# target to generate assembly for a file
src/center_stage/center_stage_capi.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/center_stage_capi.cpp.s
.PHONY : src/center_stage/center_stage_capi.cpp.s

src/center_stage/czcv_center_stage.o: src/center_stage/czcv_center_stage.cpp.o
.PHONY : src/center_stage/czcv_center_stage.o

# target to build an object file
src/center_stage/czcv_center_stage.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.o
.PHONY : src/center_stage/czcv_center_stage.cpp.o

src/center_stage/czcv_center_stage.i: src/center_stage/czcv_center_stage.cpp.i
.PHONY : src/center_stage/czcv_center_stage.i

# target to preprocess a source file
src/center_stage/czcv_center_stage.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.i
.PHONY : src/center_stage/czcv_center_stage.cpp.i

src/center_stage/czcv_center_stage.s: src/center_stage/czcv_center_stage.cpp.s
.PHONY : src/center_stage/czcv_center_stage.s

# target to generate assembly for a file
src/center_stage/czcv_center_stage.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/czcv_center_stage.cpp.s
.PHONY : src/center_stage/czcv_center_stage.cpp.s

src/center_stage/person_viewer.o: src/center_stage/person_viewer.cpp.o
.PHONY : src/center_stage/person_viewer.o

# target to build an object file
src/center_stage/person_viewer.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.o
.PHONY : src/center_stage/person_viewer.cpp.o

src/center_stage/person_viewer.i: src/center_stage/person_viewer.cpp.i
.PHONY : src/center_stage/person_viewer.i

# target to preprocess a source file
src/center_stage/person_viewer.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.i
.PHONY : src/center_stage/person_viewer.cpp.i

src/center_stage/person_viewer.s: src/center_stage/person_viewer.cpp.s
.PHONY : src/center_stage/person_viewer.s

# target to generate assembly for a file
src/center_stage/person_viewer.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/center_stage/person_viewer.cpp.s
.PHONY : src/center_stage/person_viewer.cpp.s

src/config/config_setter.o: src/config/config_setter.cpp.o
.PHONY : src/config/config_setter.o

# target to build an object file
src/config/config_setter.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.o
.PHONY : src/config/config_setter.cpp.o

src/config/config_setter.i: src/config/config_setter.cpp.i
.PHONY : src/config/config_setter.i

# target to preprocess a source file
src/config/config_setter.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.i
.PHONY : src/config/config_setter.cpp.i

src/config/config_setter.s: src/config/config_setter.cpp.s
.PHONY : src/config/config_setter.s

# target to generate assembly for a file
src/config/config_setter.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/config/config_setter.cpp.s
.PHONY : src/config/config_setter.cpp.s

src/detector/base_detector.o: src/detector/base_detector.cpp.o
.PHONY : src/detector/base_detector.o

# target to build an object file
src/detector/base_detector.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.o
.PHONY : src/detector/base_detector.cpp.o

src/detector/base_detector.i: src/detector/base_detector.cpp.i
.PHONY : src/detector/base_detector.i

# target to preprocess a source file
src/detector/base_detector.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.i
.PHONY : src/detector/base_detector.cpp.i

src/detector/base_detector.s: src/detector/base_detector.cpp.s
.PHONY : src/detector/base_detector.s

# target to generate assembly for a file
src/detector/base_detector.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/base_detector.cpp.s
.PHONY : src/detector/base_detector.cpp.s

src/detector/detail/rknn_yolox.o: src/detector/detail/rknn_yolox.cpp.o
.PHONY : src/detector/detail/rknn_yolox.o

# target to build an object file
src/detector/detail/rknn_yolox.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.o
.PHONY : src/detector/detail/rknn_yolox.cpp.o

src/detector/detail/rknn_yolox.i: src/detector/detail/rknn_yolox.cpp.i
.PHONY : src/detector/detail/rknn_yolox.i

# target to preprocess a source file
src/detector/detail/rknn_yolox.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.i
.PHONY : src/detector/detail/rknn_yolox.cpp.i

src/detector/detail/rknn_yolox.s: src/detector/detail/rknn_yolox.cpp.s
.PHONY : src/detector/detail/rknn_yolox.s

# target to generate assembly for a file
src/detector/detail/rknn_yolox.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detail/rknn_yolox.cpp.s
.PHONY : src/detector/detail/rknn_yolox.cpp.s

src/detector/detail/tnn_yolox.o: src/detector/detail/tnn_yolox.cpp.o
.PHONY : src/detector/detail/tnn_yolox.o

# target to build an object file
src/detector/detail/tnn_yolox.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.o
.PHONY : src/detector/detail/tnn_yolox.cpp.o

src/detector/detail/tnn_yolox.i: src/detector/detail/tnn_yolox.cpp.i
.PHONY : src/detector/detail/tnn_yolox.i

# target to preprocess a source file
src/detector/detail/tnn_yolox.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.i
.PHONY : src/detector/detail/tnn_yolox.cpp.i

src/detector/detail/tnn_yolox.s: src/detector/detail/tnn_yolox.cpp.s
.PHONY : src/detector/detail/tnn_yolox.s

# target to generate assembly for a file
src/detector/detail/tnn_yolox.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detail/tnn_yolox.cpp.s
.PHONY : src/detector/detail/tnn_yolox.cpp.s

src/detector/detector_factory.o: src/detector/detector_factory.cpp.o
.PHONY : src/detector/detector_factory.o

# target to build an object file
src/detector/detector_factory.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.o
.PHONY : src/detector/detector_factory.cpp.o

src/detector/detector_factory.i: src/detector/detector_factory.cpp.i
.PHONY : src/detector/detector_factory.i

# target to preprocess a source file
src/detector/detector_factory.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.i
.PHONY : src/detector/detector_factory.cpp.i

src/detector/detector_factory.s: src/detector/detector_factory.cpp.s
.PHONY : src/detector/detector_factory.s

# target to generate assembly for a file
src/detector/detector_factory.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/detector_factory.cpp.s
.PHONY : src/detector/detector_factory.cpp.s

src/detector/yolox_person_det.o: src/detector/yolox_person_det.cpp.o
.PHONY : src/detector/yolox_person_det.o

# target to build an object file
src/detector/yolox_person_det.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.o
.PHONY : src/detector/yolox_person_det.cpp.o

src/detector/yolox_person_det.i: src/detector/yolox_person_det.cpp.i
.PHONY : src/detector/yolox_person_det.i

# target to preprocess a source file
src/detector/yolox_person_det.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.i
.PHONY : src/detector/yolox_person_det.cpp.i

src/detector/yolox_person_det.s: src/detector/yolox_person_det.cpp.s
.PHONY : src/detector/yolox_person_det.s

# target to generate assembly for a file
src/detector/yolox_person_det.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/detector/yolox_person_det.cpp.s
.PHONY : src/detector/yolox_person_det.cpp.s

src/hand/rknn_yolov10.o: src/hand/rknn_yolov10.cpp.o
.PHONY : src/hand/rknn_yolov10.o

# target to build an object file
src/hand/rknn_yolov10.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.o
.PHONY : src/hand/rknn_yolov10.cpp.o

src/hand/rknn_yolov10.i: src/hand/rknn_yolov10.cpp.i
.PHONY : src/hand/rknn_yolov10.i

# target to preprocess a source file
src/hand/rknn_yolov10.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.i
.PHONY : src/hand/rknn_yolov10.cpp.i

src/hand/rknn_yolov10.s: src/hand/rknn_yolov10.cpp.s
.PHONY : src/hand/rknn_yolov10.s

# target to generate assembly for a file
src/hand/rknn_yolov10.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/hand/rknn_yolov10.cpp.s
.PHONY : src/hand/rknn_yolov10.cpp.s

src/tracker/base_tracker.o: src/tracker/base_tracker.cpp.o
.PHONY : src/tracker/base_tracker.o

# target to build an object file
src/tracker/base_tracker.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.o
.PHONY : src/tracker/base_tracker.cpp.o

src/tracker/base_tracker.i: src/tracker/base_tracker.cpp.i
.PHONY : src/tracker/base_tracker.i

# target to preprocess a source file
src/tracker/base_tracker.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.i
.PHONY : src/tracker/base_tracker.cpp.i

src/tracker/base_tracker.s: src/tracker/base_tracker.cpp.s
.PHONY : src/tracker/base_tracker.s

# target to generate assembly for a file
src/tracker/base_tracker.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/base_tracker.cpp.s
.PHONY : src/tracker/base_tracker.cpp.s

src/tracker/byte_tracker.o: src/tracker/byte_tracker.cpp.o
.PHONY : src/tracker/byte_tracker.o

# target to build an object file
src/tracker/byte_tracker.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.o
.PHONY : src/tracker/byte_tracker.cpp.o

src/tracker/byte_tracker.i: src/tracker/byte_tracker.cpp.i
.PHONY : src/tracker/byte_tracker.i

# target to preprocess a source file
src/tracker/byte_tracker.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.i
.PHONY : src/tracker/byte_tracker.cpp.i

src/tracker/byte_tracker.s: src/tracker/byte_tracker.cpp.s
.PHONY : src/tracker/byte_tracker.s

# target to generate assembly for a file
src/tracker/byte_tracker.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/byte_tracker.cpp.s
.PHONY : src/tracker/byte_tracker.cpp.s

src/tracker/detail/kalmanFilter.o: src/tracker/detail/kalmanFilter.cpp.o
.PHONY : src/tracker/detail/kalmanFilter.o

# target to build an object file
src/tracker/detail/kalmanFilter.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.o
.PHONY : src/tracker/detail/kalmanFilter.cpp.o

src/tracker/detail/kalmanFilter.i: src/tracker/detail/kalmanFilter.cpp.i
.PHONY : src/tracker/detail/kalmanFilter.i

# target to preprocess a source file
src/tracker/detail/kalmanFilter.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.i
.PHONY : src/tracker/detail/kalmanFilter.cpp.i

src/tracker/detail/kalmanFilter.s: src/tracker/detail/kalmanFilter.cpp.s
.PHONY : src/tracker/detail/kalmanFilter.s

# target to generate assembly for a file
src/tracker/detail/kalmanFilter.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/detail/kalmanFilter.cpp.s
.PHONY : src/tracker/detail/kalmanFilter.cpp.s

src/tracker/person_assert/person_assert.o: src/tracker/person_assert/person_assert.cpp.o
.PHONY : src/tracker/person_assert/person_assert.o

# target to build an object file
src/tracker/person_assert/person_assert.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.o
.PHONY : src/tracker/person_assert/person_assert.cpp.o

src/tracker/person_assert/person_assert.i: src/tracker/person_assert/person_assert.cpp.i
.PHONY : src/tracker/person_assert/person_assert.i

# target to preprocess a source file
src/tracker/person_assert/person_assert.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.i
.PHONY : src/tracker/person_assert/person_assert.cpp.i

src/tracker/person_assert/person_assert.s: src/tracker/person_assert/person_assert.cpp.s
.PHONY : src/tracker/person_assert/person_assert.s

# target to generate assembly for a file
src/tracker/person_assert/person_assert.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/person_assert.cpp.s
.PHONY : src/tracker/person_assert/person_assert.cpp.s

src/tracker/person_assert/rknn_person_assert.o: src/tracker/person_assert/rknn_person_assert.cpp.o
.PHONY : src/tracker/person_assert/rknn_person_assert.o

# target to build an object file
src/tracker/person_assert/rknn_person_assert.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.o
.PHONY : src/tracker/person_assert/rknn_person_assert.cpp.o

src/tracker/person_assert/rknn_person_assert.i: src/tracker/person_assert/rknn_person_assert.cpp.i
.PHONY : src/tracker/person_assert/rknn_person_assert.i

# target to preprocess a source file
src/tracker/person_assert/rknn_person_assert.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.i
.PHONY : src/tracker/person_assert/rknn_person_assert.cpp.i

src/tracker/person_assert/rknn_person_assert.s: src/tracker/person_assert/rknn_person_assert.cpp.s
.PHONY : src/tracker/person_assert/rknn_person_assert.s

# target to generate assembly for a file
src/tracker/person_assert/rknn_person_assert.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/person_assert/rknn_person_assert.cpp.s
.PHONY : src/tracker/person_assert/rknn_person_assert.cpp.s

src/tracker/tracker_factory.o: src/tracker/tracker_factory.cpp.o
.PHONY : src/tracker/tracker_factory.o

# target to build an object file
src/tracker/tracker_factory.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.o
.PHONY : src/tracker/tracker_factory.cpp.o

src/tracker/tracker_factory.i: src/tracker/tracker_factory.cpp.i
.PHONY : src/tracker/tracker_factory.i

# target to preprocess a source file
src/tracker/tracker_factory.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.i
.PHONY : src/tracker/tracker_factory.cpp.i

src/tracker/tracker_factory.s: src/tracker/tracker_factory.cpp.s
.PHONY : src/tracker/tracker_factory.s

# target to generate assembly for a file
src/tracker/tracker_factory.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/tracker/tracker_factory.cpp.s
.PHONY : src/tracker/tracker_factory.cpp.s

src/utils/hungarian_match.o: src/utils/hungarian_match.cpp.o
.PHONY : src/utils/hungarian_match.o

# target to build an object file
src/utils/hungarian_match.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.o
.PHONY : src/utils/hungarian_match.cpp.o

src/utils/hungarian_match.i: src/utils/hungarian_match.cpp.i
.PHONY : src/utils/hungarian_match.i

# target to preprocess a source file
src/utils/hungarian_match.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.i
.PHONY : src/utils/hungarian_match.cpp.i

src/utils/hungarian_match.s: src/utils/hungarian_match.cpp.s
.PHONY : src/utils/hungarian_match.s

# target to generate assembly for a file
src/utils/hungarian_match.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/utils/hungarian_match.cpp.s
.PHONY : src/utils/hungarian_match.cpp.s

src/utils/lapjv.o: src/utils/lapjv.cpp.o
.PHONY : src/utils/lapjv.o

# target to build an object file
src/utils/lapjv.cpp.o:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.o
.PHONY : src/utils/lapjv.cpp.o

src/utils/lapjv.i: src/utils/lapjv.cpp.i
.PHONY : src/utils/lapjv.i

# target to preprocess a source file
src/utils/lapjv.cpp.i:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.i
.PHONY : src/utils/lapjv.cpp.i

src/utils/lapjv.s: src/utils/lapjv.cpp.s
.PHONY : src/utils/lapjv.s

# target to generate assembly for a file
src/utils/lapjv.cpp.s:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/src/utils/lapjv.cpp.s
.PHONY : src/utils/lapjv.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... czcv_camera"
	@echo "... src/base/abstract_model.o"
	@echo "... src/base/abstract_model.i"
	@echo "... src/base/abstract_model.s"
	@echo "... src/base/aes256.o"
	@echo "... src/base/aes256.i"
	@echo "... src/base/aes256.s"
	@echo "... src/base/common.o"
	@echo "... src/base/common.i"
	@echo "... src/base/common.s"
	@echo "... src/base/macro.o"
	@echo "... src/base/macro.i"
	@echo "... src/base/macro.s"
	@echo "... src/base/mem_allocator.o"
	@echo "... src/base/mem_allocator.i"
	@echo "... src/base/mem_allocator.s"
	@echo "... src/base/nms.o"
	@echo "... src/base/nms.i"
	@echo "... src/base/nms.s"
	@echo "... src/base/status.o"
	@echo "... src/base/status.i"
	@echo "... src/base/status.s"
	@echo "... src/center_stage/cam_dewarper.o"
	@echo "... src/center_stage/cam_dewarper.i"
	@echo "... src/center_stage/cam_dewarper.s"
	@echo "... src/center_stage/center_stage_api.o"
	@echo "... src/center_stage/center_stage_api.i"
	@echo "... src/center_stage/center_stage_api.s"
	@echo "... src/center_stage/center_stage_capi.o"
	@echo "... src/center_stage/center_stage_capi.i"
	@echo "... src/center_stage/center_stage_capi.s"
	@echo "... src/center_stage/czcv_center_stage.o"
	@echo "... src/center_stage/czcv_center_stage.i"
	@echo "... src/center_stage/czcv_center_stage.s"
	@echo "... src/center_stage/person_viewer.o"
	@echo "... src/center_stage/person_viewer.i"
	@echo "... src/center_stage/person_viewer.s"
	@echo "... src/config/config_setter.o"
	@echo "... src/config/config_setter.i"
	@echo "... src/config/config_setter.s"
	@echo "... src/detector/base_detector.o"
	@echo "... src/detector/base_detector.i"
	@echo "... src/detector/base_detector.s"
	@echo "... src/detector/detail/rknn_yolox.o"
	@echo "... src/detector/detail/rknn_yolox.i"
	@echo "... src/detector/detail/rknn_yolox.s"
	@echo "... src/detector/detail/tnn_yolox.o"
	@echo "... src/detector/detail/tnn_yolox.i"
	@echo "... src/detector/detail/tnn_yolox.s"
	@echo "... src/detector/detector_factory.o"
	@echo "... src/detector/detector_factory.i"
	@echo "... src/detector/detector_factory.s"
	@echo "... src/detector/yolox_person_det.o"
	@echo "... src/detector/yolox_person_det.i"
	@echo "... src/detector/yolox_person_det.s"
	@echo "... src/hand/rknn_yolov10.o"
	@echo "... src/hand/rknn_yolov10.i"
	@echo "... src/hand/rknn_yolov10.s"
	@echo "... src/tracker/base_tracker.o"
	@echo "... src/tracker/base_tracker.i"
	@echo "... src/tracker/base_tracker.s"
	@echo "... src/tracker/byte_tracker.o"
	@echo "... src/tracker/byte_tracker.i"
	@echo "... src/tracker/byte_tracker.s"
	@echo "... src/tracker/detail/kalmanFilter.o"
	@echo "... src/tracker/detail/kalmanFilter.i"
	@echo "... src/tracker/detail/kalmanFilter.s"
	@echo "... src/tracker/person_assert/person_assert.o"
	@echo "... src/tracker/person_assert/person_assert.i"
	@echo "... src/tracker/person_assert/person_assert.s"
	@echo "... src/tracker/person_assert/rknn_person_assert.o"
	@echo "... src/tracker/person_assert/rknn_person_assert.i"
	@echo "... src/tracker/person_assert/rknn_person_assert.s"
	@echo "... src/tracker/tracker_factory.o"
	@echo "... src/tracker/tracker_factory.i"
	@echo "... src/tracker/tracker_factory.s"
	@echo "... src/utils/hungarian_match.o"
	@echo "... src/utils/hungarian_match.i"
	@echo "... src/utils/hungarian_match.s"
	@echo "... src/utils/lapjv.o"
	@echo "... src/utils/lapjv.i"
	@echo "... src/utils/lapjv.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

