# CMAKE generated file: DO NOT EDIT!
# Generated by "MSYS Makefiles" Generator, CMake Version 3.25

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = "/D/Program Files/CMake/bin/cmake.exe"

# The command to remove a file.
RM = "/D/Program Files/CMake/bin/cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /D/Program/Project/project/czcv_camera_new

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: lib/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: lib/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: lib/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory lib

# Recursive "all" directory target.
lib/all: lib/CMakeFiles/czcv_camera.dir/all
.PHONY : lib/all

# Recursive "preinstall" directory target.
lib/preinstall:
.PHONY : lib/preinstall

# Recursive "clean" directory target.
lib/clean: lib/CMakeFiles/czcv_camera.dir/clean
.PHONY : lib/clean

#=============================================================================
# Target rules for target lib/CMakeFiles/czcv_camera.dir

# All Build rule for target.
lib/CMakeFiles/czcv_camera.dir/all:
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/depend
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28 "Built target czcv_camera"
.PHONY : lib/CMakeFiles/czcv_camera.dir/all

# Build rule for subdir invocation for target.
lib/CMakeFiles/czcv_camera.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles 28
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 lib/CMakeFiles/czcv_camera.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /D/Program/Project/project/czcv_camera_new/build_android_arm64-v8a/CMakeFiles 0
.PHONY : lib/CMakeFiles/czcv_camera.dir/rule

# Convenience name for target.
czcv_camera: lib/CMakeFiles/czcv_camera.dir/rule
.PHONY : czcv_camera

# clean rule for target.
lib/CMakeFiles/czcv_camera.dir/clean:
	$(MAKE) $(MAKESILENT) -f lib/CMakeFiles/czcv_camera.dir/build.make lib/CMakeFiles/czcv_camera.dir/clean
.PHONY : lib/CMakeFiles/czcv_camera.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

