#!/usr/bin/env python
"""
Point Collector - 使用鼠标点击收集图像上的点坐标
使用方法: python point_collector.py <directory_path>
操作说明:
- 左键点击图像上的点来收集坐标
- 按 SPACE 键进入下一张图片
- 按 ESC 键退出程序
"""

import cv2 as cv
import numpy as np
import os
import sys
import glob
import argparse

class PointCollector:
    def __init__(self):
        self.points = []
        self.current_image = None
        self.current_filename = ""
        self.all_results = {}
        
    def mouse_callback(self, event, x, y, flags, param):
        if event == cv.EVENT_LBUTTONDOWN:
            # 记录点击的坐标
            self.points.append((x, y))
            print(f"点击坐标: ({x}, {y})")
            
            # 在图像上绘制点
            cv.circle(self.current_image, (x, y), 3, (0, 255, 0), -1)
            cv.imshow("Image", self.current_image)
    
    def process_directory(self, directory_path):
        # 获取目录中所有jpg文件
        jpg_files = glob.glob(os.path.join(directory_path, "*.jpg"))
        jpg_files.extend(glob.glob(os.path.join(directory_path, "*.JPG")))
        
        if not jpg_files:
            print("目录中没有找到jpg文件")
            return
        
        print(f"找到 {len(jpg_files)} 个jpg文件")
        
        # 创建窗口并设置鼠标回调
        cv.namedWindow("Image", cv.WINDOW_NORMAL)
        cv.setMouseCallback("Image", self.mouse_callback)
        
        for img_path in jpg_files:
            filename = os.path.basename(img_path)
            print(f"\n处理图片: {filename}")
            print("左键点击收集坐标，SPACE键下一张，ESC键退出")
            
            # 读取图像
            img = cv.imread(img_path)
            if img is None:
                print(f"无法读取图片: {img_path}")
                continue
            
            # 重置当前图片的状态
            self.current_image = img.copy()
            self.current_filename = filename
            self.points = []
            
            # 显示图像
            cv.imshow("Image", self.current_image)
            
            # 等待用户操作
            while True:
                key = cv.waitKey(0) & 0xFF
                if key == 27:  # ESC键退出
                    self.save_results(directory_path)
                    cv.destroyAllWindows()
                    return
                elif key == 32:  # SPACE键下一张
                    break
            
            # 保存当前图片的点坐标
            if self.points:
                self.all_results[filename] = self.points.copy()
                print(f"收集到 {len(self.points)} 个点")
        
        # 保存所有结果
        self.save_results(directory_path)
        cv.destroyAllWindows()
    
    def save_results(self, directory_path):
        output_file = os.path.join(directory_path, "points_output.txt")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, (filename, points) in enumerate(self.all_results.items()):
                if i > 0:
                    f.write("\n\n")
                
                # 格式化输出
                f.write(f'{{"{filename}", {{')
                
                point_strs = []
                for x, y in points:
                    point_strs.append(f"Point2f({x}, {y})")
                
                f.write(", ".join(point_strs))
                f.write("}}")
                
                if i < len(self.all_results) - 1:
                    f.write(",")
        
        print(f"\n结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='收集图像上的点击坐标')
    parser.add_argument('directory', help='包含jpg文件的目录路径')
    
    args = parser.parse_args()
    
    if not os.path.isdir(args.directory):
        print(f"错误: {args.directory} 不是有效的目录")
        sys.exit(1)
    
    collector = PointCollector()
    collector.process_directory(args.directory)

if __name__ == '__main__':
    print(__doc__)
    main()